{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena", "start-mcp-server"], "alwaysAllow": ["replace_regex", "check_onboarding_performed", "onboarding", "prepare_for_new_conversation", "think_about_whether_you_are_done", "create_text_file", "list_dir", "find_file", "search_for_pattern", "restart_language_server", "get_symbols_overview", "find_symbol", "activate_project", "read_file"]}}}