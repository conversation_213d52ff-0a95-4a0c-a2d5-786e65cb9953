{"name": "next-blog-app", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.13.1", "scripts": {"dev": "turbo run dev", "dev:display": "turbo run dev --filter=content-display-app", "dev:config": "turbo run dev --filter=content-config-app", "dev:stripe": "stripe listen --forward-to localhost:3000/api/subscription/webhook", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "dotenv": "^17.2.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"turbo": "^2.5.5", "typescript": "^5"}}