import dotenv from "dotenv";
import { Jim<PERSON> } from "jimp";
dotenv.config();

async function main() {
  const bucket = new (await import("@/libs/s3")).ImageBucket();
  const list = await bucket.listFiles();

  for (const entry of list.Contents ?? []) {
    const { Key } = entry;
    const url = `https://shoshin-space.s3.ap-southeast-1.amazonaws.com/${Key}`;

    const res = await fetch(url);
    const buffer = await res.arrayBuffer();

    // Load image with Jimp
    const image = await Jimp.fromBuffer(Buffer.from(buffer));

    // Resize to 800x800 with fit inside and convert to JPEG
    const image800 = image.clone().resize({ w: 800, h: 800 });
    const buffer800 = await image800.getBuffer("image/jpeg", { quality: 80 });

    // Resize to 400x400 with fit inside and convert to JPEG
    const image400 = image.clone().resize({ w: 400, h: 400 });
    const buffer400 = await image400.getBuffer("image/jpeg", { quality: 80 });

    await bucket.upload({ key: `resize800/${Key}`, buffer: buffer800 });
    await bucket.upload({ key: `resize400/${Key}`, buffer: buffer400 });
  }
}
main();
