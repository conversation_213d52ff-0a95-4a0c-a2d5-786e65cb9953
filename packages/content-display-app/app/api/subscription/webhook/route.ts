import { NextRequest, NextResponse } from "next/server";
import { stripe } from "ui/src/utils/stripe";
import <PERSON><PERSON> from "stripe";
import { prisma } from "prisma-db";

export async function POST(request: NextRequest) {
  try {
    const rawBody = await request.text();
    const signature = request.headers.get("stripe-signature");

    let event: Stripe.Event | undefined;
    try {
      event = stripe.webhooks.constructEvent(
        rawBody,
        signature!,
        process.env.STRIPE_WEBHOOK_SECRET!,
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        return NextResponse.json({ message: "Webhook Error" }, { status: 400 });
      }
    }
    if (event == null) {
      return NextResponse.json({ message: "event is null" }, { status: 400 });
    }

    // Handle the checkout.session.completed event
    if (event.type === "checkout.session.completed") {
      const session: Stripe.Checkout.Session = event.data.object;
      const userId = session.metadata?.user_id;

      // Update the user's subscription status in your database
      await prisma.user.update({
        where: { id: userId },
        data: {
          stripeCustomerId: session.customer?.toString(),
          stripeSubscriptionId: session.subscription?.toString(),
        },
      });
    }

    if (event.type === "customer.subscription.updated") {
    }

    if (event.type === "customer.subscription.deleted") {
    }

    return NextResponse.json({ message: "success" });
  } catch (error: unknown) {
    return NextResponse.json(
      { message: error instanceof Error ? error.message : "" },
      { status: 500 },
    );
  }
}
