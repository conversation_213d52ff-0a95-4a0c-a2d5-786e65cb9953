import { auth } from "../../../../auth";
import { stripe } from "ui/src/utils/stripe";
import { NextResponse } from "next/server";

async function manageBilling(customer_id: string | undefined | null) {
  if (customer_id == null) return;

  const res = await stripe.billingPortal.sessions.create({
    customer: customer_id,
    return_url: process.env.SITE_URL,
  });

  return res;
}

export type ManageBillingResponse = Awaited<ReturnType<typeof manageBilling>>;

export const POST = async () => {
  const session = await auth();
  const customerId = session?.user?.stripeCustomerId;

  if (!customerId) {
    return new Response("Not authorized", { status: 401 });
  }

  const res = await manageBilling(customerId);
  return NextResponse.json(res);
};
