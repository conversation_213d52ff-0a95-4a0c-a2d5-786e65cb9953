"use server";
import { LANG_PATHS } from "../../utils";
import { Space } from "prisma-db";
import Link from "next/link";
import { Fragment } from "react";

export const Footer = async ({ space }: { space?: Space | null }) => {
  const { title, description } = space ?? {};

  return (
    <footer className="mt-4">
      <div className="flex flex-wrap gap-4 items-end justify-between min-h-[40px] px-6 py-4">
        <div className="space-y-10">
          {description && (
            <div className="space-y-2 w-full sm:w-96">
              <h3 className="text-xl font-bold">{title}</h3>
              <p className="text-sm font-light text-secondary-foreground leading-loose">
                {description}
              </p>
            </div>
          )}
        </div>
        <div className="text-sm text-secondary-foreground leading-loose text-right">
          <div className="text-sm">
            {LANG_PATHS.map(({ label, path }, index) => (
              <Fragment key={path}>
                {index > 0 ? <span className="mx-1">/</span> : null}
                <Link
                  key={path}
                  href={path}
                  className="text-sm text-secondary-foreground leading-loose"
                >
                  {label}
                </Link>
              </Fragment>
            ))}
          </div>
          &copy; 2024 {title}. All right reserved
        </div>
      </div>
    </footer>
  );
};
