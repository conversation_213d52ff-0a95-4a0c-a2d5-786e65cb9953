"use client";
import { LightningBoltIcon } from "@radix-ui/react-icons";
import { useTransition } from "react";
import { cx } from "../../../../ui/src/utils/cx";
import { loadStripe } from "@stripe/stripe-js";

export const StripeCheckout = () => {
  const [isPending, startTransition] = useTransition();

  const handleCheckout = (e: React.FormEvent) => {
    e.preventDefault();

    startTransition(async () => {
      const res = await fetch("/api/subscription/checkout", { method: "POST" });
      const { id } = await res.json();
      const stripe = await loadStripe(
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
      );
      await stripe?.redirectToCheckout({ sessionId: id });
    });
  };

  return (
    <div
      className={cx(
        "h-96 w-full flex items-center justify-center",
        isPending && "animate-pulse",
      )}
    >
      <button
        className="px-8 py-6 rounded-lg ring-2 ring-lime-500"
        disabled={isPending}
        onClick={handleCheckout}
      >
        <div>
          <span className="flex items-center gap-2 text-2xl font-bold text-primary">
            <LightningBoltIcon
              className={cx(
                "w-5 h-5",
                isPending ? "animate-spin" : "animate-bounce",
              )}
            />{" "}
            Upgrade to Pro
          </span>
          <span>Unlock all blog content</span>
        </div>
      </button>
    </div>
  );
};
