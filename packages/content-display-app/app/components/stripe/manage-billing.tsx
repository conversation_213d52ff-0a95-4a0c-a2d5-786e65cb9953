"use client";
import type { ManageBillingResponse } from "../../api/subscription/manage-billing/route";
import { BackpackIcon } from "@radix-ui/react-icons";
import { useTransition } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

export const ManageBilling: React.FC = () => {
  const [isPending, startTransition] = useTransition();

  const onSubmit = () => {
    startTransition(async () => {
      const data: ManageBillingResponse = await fetch(
        "/api/subscription/manage-billing",
        { method: "POST" },
      ).then((res) => res.json());

      if (data?.url) {
        window.location.href = data?.url;
      }
    });
  };

  return (
    <button
      className="flex gap-2 items-center"
      disabled={isPending}
      onClick={onSubmit}
    >
      <span className="flex items-center gap-2">
        {isPending && <AiOutlineLoading3Quarters className="animate-spin" />}{" "}
        Manage Billing
      </span>
      <BackpackIcon />
    </button>
  );
};
