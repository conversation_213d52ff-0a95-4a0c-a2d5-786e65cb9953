export class ImageResize {
  private quality: number;
  private width: number;
  private height: number | undefined;
  constructor({
    width,
    height,
    quality,
  }: {
    width: number;
    quality?: number;
    height?: number;
  }) {
    this.quality = quality ?? 0.85;
    this.width = width;
    this.height = height;
  }

  async resizeFile(file: File | Blob | undefined | null) {
    return new Promise<Blob | null>((resolve, reject) => {
      const reader = new FileReader();
      const img = new Image();
      const MAX_WIDTH = this.width;
      const MAX_HEIGHT = this.height || this.width;
      const QUALITY = this.quality;

      reader.onload = function (e: ProgressEvent<FileReader>) {
        if (typeof e.target?.result === "string") {
          img.src = e.target.result;
        } else {
          reject("failed to read file");
        }
      };
      img.onload = function () {
        let width = img.width;
        let height = img.height;
        if (width > height) {
          if (width > MAX_WIDTH) {
            height *= MAX_WIDTH / width;
            width = MAX_WIDTH;
          }
        } else {
          if (height > MAX_HEIGHT) {
            width *= MAX_HEIGHT / height;
            height = MAX_HEIGHT;
          }
        }

        // Create canvas and draw the resized image
        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          return reject("get canvas failed");
        }
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to WebP and show the preview
        canvas.toBlob(resolve, "image/webp", QUALITY);
      };

      if (file) {
        reader.readAsDataURL(file);
      } else {
        reject("no file input");
      }
    });
  }
}
