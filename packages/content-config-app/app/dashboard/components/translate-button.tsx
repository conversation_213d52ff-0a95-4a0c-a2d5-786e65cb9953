import type { BlogListResponse } from "../../api/manage/blog/list/route";
import { TranslationOutlined } from "@ant-design/icons";
import { Button, Dropdown } from "antd";
import { useState } from "react";

const AVAILABLE_LANGUAGES = [
  {
    lang: "en",
    label: "English",
  },
  {
    lang: "ja",
    label: "日本語",
  },
  {
    lang: "zh",
    label: "中文",
  },
];

const addLanguageToArticle = async (
  record: BlogListResponse[0],
  lang: string,
  callback?: () => void,
) => {
  const { contents } = record;
  if (contents.some((content) => content.language === lang)) {
    return;
  }

  const source =
    contents.find(({ language }) => language === "en") || contents[0];

  await fetch("/api/assistance/translate", {
    method: "POST",
    body: JSON.stringify({
      contentId: source.id,
      language: lang,
    }),
  });

  callback?.();
};

export const TranslateButton = ({
  record,
  onSuccess,
}: {
  record: BlogListResponse[0];
  onSuccess?: () => void;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { contents } = record;
  return (
    <Dropdown
      trigger={["click"]}
      placement="bottomRight"
      menu={{
        items: AVAILABLE_LANGUAGES.map(({ lang, label }) => ({
          key: lang,
          label,
          disabled: contents.some((content) => content.language === lang),
          onClick: async ({ key }) => {
            try {
              setIsLoading(true);
              await addLanguageToArticle(record, key, onSuccess);
            } finally {
              setIsLoading(false);
            }
          },
        })),
      }}
    >
      <Button
        loading={isLoading}
        type="text"
        size="small"
        icon={<TranslationOutlined />}
      />
    </Dropdown>
  );
};
