import { Button, FormInstance, notification } from "antd";
import { useWatch } from "antd/es/form/Form";
import { useState } from "react";
import { FV } from "../[[...path]]/containers/edit-content-page";

export const SeoSuggestButton = ({ form }: { form: FormInstance<FV> }) => {
  const [notificationApi, contextHolder] = notification.useNotification();
  const [isLoading, setIsLoading] = useState(false);

  const title = useWatch("title", form);
  const content = useWatch("content", form);
  const language = useWatch("language", form);

  const getSuggestion = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/assistance/seo", {
        method: "POST",
        body: JSON.stringify({
          title,
          article: content,
          lang: language,
        }),
      });
      const data = await res.json();

      const fieldConfig: {
        label: string;
        formFieldKey: keyof FV;
        dataKey: keyof typeof data;
      }[] = [
        {
          label: "URL slug",
          formFieldKey: "slug",
          dataKey: "slug",
        },
        {
          label: "Title",
          formFieldKey: "title",
          dataKey: "title",
        },
        {
          label: "Description",
          formFieldKey: "seoDescription",
          dataKey: "description",
        },
      ];

      notificationApi.open({
        placement: "topRight",
        message: null,
        description: (
          <div className="prose dark:prose-invert">
            <h2>SEO Suggestions</h2>
            {fieldConfig.map(({ label, formFieldKey, dataKey }) => {
              const onClick = () =>
                form.setFieldValue(formFieldKey, data[dataKey]);
              return (
                <div key={formFieldKey}>
                  <h4 className="flex items-center gap-2">
                    <span>{label}</span>
                    <Button onClick={onClick} size="small">
                      apply
                    </Button>
                  </h4>
                  <p>{data[dataKey]}</p>
                </div>
              );
            })}
          </div>
        ),
        duration: 0,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <Button loading={isLoading} onClick={getSuggestion}>
        SEO suggestions
      </Button>
    </>
  );
};
