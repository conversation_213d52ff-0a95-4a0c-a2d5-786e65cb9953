"use client";
import { Layout } from "antd";
import dynamic from "next/dynamic";
import { AntDesignConfigProvider } from "../components/ant-design-provider";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { DashboardHomePage } from "./containers/dashboard-home-page";
import { EditContentPage } from "./containers/edit-content-page";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

function DashboardPage() {
  const router = createBrowserRouter(
    [
      {
        path: "/",
        element: <DashboardHomePage />,
      },
      {
        path: "/edit/:contentId?",
        element: <EditContentPage />,
      },
    ],
    { basename: "/dashboard" },
  );

  return (
    <QueryClientProvider client={queryClient}>
      <AntDesignConfigProvider>
        <Layout>
          <main className="p-12 py-4 flex-auto flex flex-col">
            <RouterProvider router={router} />
          </main>
        </Layout>
      </AntDesignConfigProvider>
    </QueryClientProvider>
  );
}

export default dynamic(() => Promise.resolve(DashboardPage), {
  ssr: false,
});
