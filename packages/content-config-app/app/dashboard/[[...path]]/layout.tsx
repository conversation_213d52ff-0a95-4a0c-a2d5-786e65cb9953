import { querySpace } from "../../api/read/space/query";
import { auth } from "../../../auth";

export async function generateMetadata() {
  const data = await querySpace();

  if (!data) {
    return {};
  }

  const { title, description } = data;

  return {
    title: "Dashboard" + " | " + title,
    description: description,
  };
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const res = await auth();
  if (res?.user.role !== "ADMIN") {
    return "Unauthorized";
  }

  return <>{children}</>;
}
